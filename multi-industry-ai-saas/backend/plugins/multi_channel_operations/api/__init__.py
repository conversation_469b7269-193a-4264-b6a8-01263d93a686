#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手插件API模块
"""

from .dashboard import router as dashboard_router
from .channels import router as channels_router
from .products import router as products_router
from .pricing import router as pricing_router
from .competitors import router as competitors_router
from .automation import router as automation_router
from .analytics import router as analytics_router
from .settings import router as settings_router
from .ai_agent import router as ai_agent_router
from .ai_image_generator import router as ai_image_generator_router
from .category_mapping import router as category_mapping_router
from .platform_activities import router as platform_activities_router
from .advanced_features import router as advanced_features_router

__all__ = [
    "dashboard_router",
    "channels_router",
    "products_router",
    "pricing_router",
    "competitors_router",
    "automation_router",
    "analytics_router",
    "settings_router",
    "ai_agent_router",
    "ai_image_generator_router",
    "category_mapping_router",
    "platform_activities_router",
    "advanced_features_router"
]
