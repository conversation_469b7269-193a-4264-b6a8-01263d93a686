#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级自动化规则引擎
集成项目现有的任务系统、WebSocket通知、Redis缓存等能力
"""

import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

# 使用模拟服务，避免导入错误
# from core.redis_client import redis_client
# from core.websocket_manager import websocket_manager
# from services.task.task_service import TaskService
# from services.notification.notification_service import NotificationService
# from models.task.task import Task

# 模拟Redis客户端
class MockRedisClient:
    def __init__(self):
        self.data = {}

    async def hset(self, key, field, value):
        if key not in self.data:
            self.data[key] = {}
        self.data[key][field] = value

    async def hget(self, key, field):
        return self.data.get(key, {}).get(field)

    async def hgetall(self, key):
        return self.data.get(key, {})

    async def lpush(self, key, value):
        if key not in self.data:
            self.data[key] = []
        self.data[key].insert(0, value)

    async def ltrim(self, key, start, end):
        if key in self.data:
            self.data[key] = self.data[key][start:end+1]

    async def lrange(self, key, start, end):
        return self.data.get(key, [])[start:end+1] if end >= 0 else self.data.get(key, [])[start:]

# 模拟WebSocket管理器
class MockWebSocketManager:
    async def send_to_project(self, project_id, message):
        print(f"WebSocket message to {project_id}: {message}")

# 模拟服务
class MockTaskService:
    async def create_task(self, db, task_data, project_id):
        return {'task_id': f'task_{project_id}_{task_data["title"][:10]}'}

class MockNotificationService:
    async def create_notification(self, db, notification_data):
        return {'notification_id': f'notif_{notification_data["title"][:10]}'}

redis_client = MockRedisClient()
websocket_manager = MockWebSocketManager()
from .ml_integration_service import MLIntegrationService

logger = logging.getLogger(__name__)

class AdvancedAutomationService:
    """高级自动化规则引擎"""
    
    def __init__(self):
        self.task_service = MockTaskService()
        self.notification_service = MockNotificationService()
        self.ml_service = MLIntegrationService()
        self.rule_cache_key = "automation_rules"
        self.execution_cache_key = "automation_executions"
    
    async def create_advanced_rule(self, db: AsyncSession, project_id: str, 
                                 rule_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建高级自动化规则"""
        try:
            # 验证规则配置
            validation_result = await self._validate_rule_config(rule_config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }
            
            # 生成规则ID
            rule_id = f"rule_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 规则配置结构
            rule_data = {
                'id': rule_id,
                'project_id': project_id,
                'name': rule_config['name'],
                'description': rule_config.get('description', ''),
                'type': rule_config['type'],  # 'condition_based', 'ml_based', 'time_based', 'event_based'
                'priority': rule_config.get('priority', 5),
                'is_active': rule_config.get('is_active', True),
                'conditions': rule_config['conditions'],
                'actions': rule_config['actions'],
                'ml_config': rule_config.get('ml_config', {}),
                'schedule': rule_config.get('schedule', {}),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'execution_count': 0,
                'last_execution': None,
                'success_rate': 1.0
            }
            
            # 保存到Redis缓存
            await redis_client.hset(
                f"{self.rule_cache_key}:{project_id}",
                rule_id,
                json.dumps(rule_data)
            )
            
            # 创建系统任务来监控规则
            task_data = {
                'title': f"自动化规则监控: {rule_config['name']}",
                'description': f"监控和执行自动化规则: {rule_config['name']}",
                'task_type': 'automation_rule',
                'priority': 'medium',
                'metadata': {
                    'rule_id': rule_id,
                    'project_id': project_id,
                    'rule_type': rule_config['type']
                }
            }
            
            task_result = await self.task_service.create_task(db, task_data, project_id)
            
            # 发送WebSocket通知
            await websocket_manager.send_to_project(
                project_id,
                {
                    'type': 'automation_rule_created',
                    'data': {
                        'rule_id': rule_id,
                        'rule_name': rule_config['name'],
                        'task_id': task_result.get('task_id')
                    }
                }
            )
            
            return {
                'success': True,
                'data': {
                    'rule_id': rule_id,
                    'rule_data': rule_data,
                    'task_id': task_result.get('task_id')
                }
            }
            
        except Exception as e:
            logger.error(f"创建自动化规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def execute_rule(self, db: AsyncSession, project_id: str, 
                          rule_id: str, context_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行自动化规则"""
        try:
            # 获取规则配置
            rule_data_str = await redis_client.hget(f"{self.rule_cache_key}:{project_id}", rule_id)
            if not rule_data_str:
                return {
                    'success': False,
                    'error': '规则不存在'
                }
            
            rule_data = json.loads(rule_data_str)
            
            if not rule_data.get('is_active', True):
                return {
                    'success': False,
                    'error': '规则已禁用'
                }
            
            # 记录执行开始
            execution_id = f"exec_{rule_id}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            execution_start = datetime.now()
            
            # 根据规则类型执行不同逻辑
            execution_result = None
            
            if rule_data['type'] == 'condition_based':
                execution_result = await self._execute_condition_based_rule(
                    db, project_id, rule_data, context_data
                )
            elif rule_data['type'] == 'ml_based':
                execution_result = await self._execute_ml_based_rule(
                    db, project_id, rule_data, context_data
                )
            elif rule_data['type'] == 'time_based':
                execution_result = await self._execute_time_based_rule(
                    db, project_id, rule_data, context_data
                )
            elif rule_data['type'] == 'event_based':
                execution_result = await self._execute_event_based_rule(
                    db, project_id, rule_data, context_data
                )
            else:
                return {
                    'success': False,
                    'error': f'不支持的规则类型: {rule_data["type"]}'
                }
            
            # 记录执行结果
            execution_end = datetime.now()
            execution_duration = (execution_end - execution_start).total_seconds()
            
            execution_record = {
                'execution_id': execution_id,
                'rule_id': rule_id,
                'project_id': project_id,
                'start_time': execution_start.isoformat(),
                'end_time': execution_end.isoformat(),
                'duration': execution_duration,
                'success': execution_result['success'],
                'result': execution_result,
                'context_data': context_data or {}
            }
            
            # 保存执行记录
            await redis_client.lpush(
                f"{self.execution_cache_key}:{project_id}:{rule_id}",
                json.dumps(execution_record)
            )
            
            # 限制执行记录数量
            await redis_client.ltrim(f"{self.execution_cache_key}:{project_id}:{rule_id}", 0, 99)
            
            # 更新规则统计
            rule_data['execution_count'] += 1
            rule_data['last_execution'] = execution_end.isoformat()
            
            # 计算成功率
            recent_executions = await redis_client.lrange(
                f"{self.execution_cache_key}:{project_id}:{rule_id}", 0, 9
            )
            
            if recent_executions:
                success_count = sum(
                    1 for exec_str in recent_executions
                    if json.loads(exec_str).get('success', False)
                )
                rule_data['success_rate'] = success_count / len(recent_executions)
            
            # 更新规则数据
            await redis_client.hset(
                f"{self.rule_cache_key}:{project_id}",
                rule_id,
                json.dumps(rule_data)
            )
            
            # 发送WebSocket通知
            await websocket_manager.send_to_project(
                project_id,
                {
                    'type': 'automation_rule_executed',
                    'data': {
                        'rule_id': rule_id,
                        'execution_id': execution_id,
                        'success': execution_result['success'],
                        'duration': execution_duration,
                        'result': execution_result
                    }
                }
            )
            
            return {
                'success': True,
                'data': {
                    'execution_id': execution_id,
                    'execution_result': execution_result,
                    'duration': execution_duration
                }
            }
            
        except Exception as e:
            logger.error(f"执行自动化规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_condition_based_rule(self, db: AsyncSession, project_id: str,
                                          rule_data: Dict, context_data: Dict) -> Dict[str, Any]:
        """执行基于条件的规则"""
        try:
            conditions = rule_data['conditions']
            actions = rule_data['actions']
            
            # 评估条件
            condition_results = []
            for condition in conditions:
                result = await self._evaluate_condition(condition, context_data)
                condition_results.append(result)
            
            # 根据逻辑操作符决定是否执行动作
            logic_operator = rule_data.get('logic_operator', 'AND')
            should_execute = False
            
            if logic_operator == 'AND':
                should_execute = all(condition_results)
            elif logic_operator == 'OR':
                should_execute = any(condition_results)
            
            executed_actions = []
            if should_execute:
                for action in actions:
                    action_result = await self._execute_action(db, project_id, action, context_data)
                    executed_actions.append(action_result)
            
            return {
                'success': True,
                'condition_results': condition_results,
                'should_execute': should_execute,
                'executed_actions': executed_actions
            }
            
        except Exception as e:
            logger.error(f"执行条件规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_ml_based_rule(self, db: AsyncSession, project_id: str,
                                   rule_data: Dict, context_data: Dict) -> Dict[str, Any]:
        """执行基于机器学习的规则"""
        try:
            ml_config = rule_data['ml_config']
            ml_type = ml_config.get('type', 'prediction')
            
            ml_result = None
            
            if ml_type == 'sales_prediction':
                ml_result = await self.ml_service.sales_prediction(
                    db, project_id, 
                    context_data.get('historical_data', []),
                    ml_config.get('prediction_days', 7)
                )
            elif ml_type == 'price_optimization':
                ml_result = await self.ml_service.price_optimization(
                    db, project_id,
                    context_data.get('product_data', {}),
                    context_data.get('market_data', {})
                )
            elif ml_type == 'customer_behavior':
                ml_result = await self.ml_service.customer_behavior_analysis(
                    db, project_id,
                    context_data.get('customer_data', [])
                )
            
            # 根据ML结果决定是否执行动作
            threshold = ml_config.get('threshold', 0.7)
            confidence = ml_result.get('data', {}).get('model_info', {}).get('confidence_level', 0)
            
            executed_actions = []
            if confidence >= threshold:
                for action in rule_data['actions']:
                    action_context = {**context_data, 'ml_result': ml_result}
                    action_result = await self._execute_action(db, project_id, action, action_context)
                    executed_actions.append(action_result)
            
            return {
                'success': True,
                'ml_result': ml_result,
                'confidence': confidence,
                'threshold': threshold,
                'executed_actions': executed_actions
            }
            
        except Exception as e:
            logger.error(f"执行ML规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_time_based_rule(self, db: AsyncSession, project_id: str,
                                     rule_data: Dict, context_data: Dict) -> Dict[str, Any]:
        """执行基于时间的规则"""
        try:
            schedule = rule_data['schedule']
            current_time = datetime.now()
            
            # 检查时间条件
            should_execute = False
            
            if schedule.get('type') == 'cron':
                # 简化的cron检查（实际应用中应使用专业的cron库）
                should_execute = True  # 简化处理
            elif schedule.get('type') == 'interval':
                last_execution = rule_data.get('last_execution')
                if last_execution:
                    last_time = datetime.fromisoformat(last_execution)
                    interval_minutes = schedule.get('interval_minutes', 60)
                    should_execute = (current_time - last_time).total_seconds() >= interval_minutes * 60
                else:
                    should_execute = True
            
            executed_actions = []
            if should_execute:
                for action in rule_data['actions']:
                    action_result = await self._execute_action(db, project_id, action, context_data)
                    executed_actions.append(action_result)
            
            return {
                'success': True,
                'should_execute': should_execute,
                'current_time': current_time.isoformat(),
                'executed_actions': executed_actions
            }
            
        except Exception as e:
            logger.error(f"执行时间规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_event_based_rule(self, db: AsyncSession, project_id: str,
                                      rule_data: Dict, context_data: Dict) -> Dict[str, Any]:
        """执行基于事件的规则"""
        try:
            event_config = rule_data.get('event_config', {})
            trigger_events = event_config.get('trigger_events', [])
            current_event = context_data.get('event_type')
            
            should_execute = current_event in trigger_events
            
            executed_actions = []
            if should_execute:
                for action in rule_data['actions']:
                    action_result = await self._execute_action(db, project_id, action, context_data)
                    executed_actions.append(action_result)
            
            return {
                'success': True,
                'should_execute': should_execute,
                'trigger_event': current_event,
                'executed_actions': executed_actions
            }
            
        except Exception as e:
            logger.error(f"执行事件规则失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _validate_rule_config(self, rule_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证规则配置"""
        try:
            required_fields = ['name', 'type', 'conditions', 'actions']
            for field in required_fields:
                if field not in rule_config:
                    return {
                        'valid': False,
                        'error': f'缺少必需字段: {field}'
                    }

            # 验证规则类型
            valid_types = ['condition_based', 'ml_based', 'time_based', 'event_based']
            if rule_config['type'] not in valid_types:
                return {
                    'valid': False,
                    'error': f'无效的规则类型: {rule_config["type"]}'
                }

            return {'valid': True}

        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }

    async def _evaluate_condition(self, condition: Dict[str, Any], context_data: Dict[str, Any]) -> bool:
        """评估单个条件"""
        try:
            field = condition['field']
            operator = condition['operator']
            value = condition['value']

            context_value = context_data.get(field)

            if operator == 'eq':
                return context_value == value
            elif operator == 'ne':
                return context_value != value
            elif operator == 'gt':
                return float(context_value) > float(value)
            elif operator == 'lt':
                return float(context_value) < float(value)
            elif operator == 'gte':
                return float(context_value) >= float(value)
            elif operator == 'lte':
                return float(context_value) <= float(value)
            elif operator == 'contains':
                return str(value) in str(context_value)
            elif operator == 'in':
                return context_value in value
            else:
                return False

        except Exception as e:
            logger.error(f"评估条件失败: {e}")
            return False

    async def _execute_action(self, db: AsyncSession, project_id: str,
                            action: Dict[str, Any], context_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个动作"""
        try:
            action_type = action['type']
            action_params = action.get('params', {})

            if action_type == 'send_notification':
                return await self._send_notification_action(db, project_id, action_params, context_data)
            elif action_type == 'create_task':
                return await self._create_task_action(db, project_id, action_params, context_data)
            elif action_type == 'update_price':
                return await self._update_price_action(db, project_id, action_params, context_data)
            elif action_type == 'update_inventory':
                return await self._update_inventory_action(db, project_id, action_params, context_data)
            elif action_type == 'webhook':
                return await self._webhook_action(action_params, context_data)
            else:
                return {
                    'success': False,
                    'error': f'不支持的动作类型: {action_type}'
                }

        except Exception as e:
            logger.error(f"执行动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _send_notification_action(self, db: AsyncSession, project_id: str,
                                      params: Dict[str, Any], context_data: Dict[str, Any]) -> Dict[str, Any]:
        """发送通知动作"""
        try:
            notification_data = {
                'title': params.get('title', '自动化规则通知'),
                'message': params.get('message', '').format(**context_data),
                'type': params.get('notification_type', 'info'),
                'project_id': project_id
            }

            # 使用项目的通知服务
            result = await self.notification_service.create_notification(db, notification_data)

            # 发送WebSocket通知
            await websocket_manager.send_to_project(
                project_id,
                {
                    'type': 'automation_notification',
                    'data': notification_data
                }
            )

            return {
                'success': True,
                'action_type': 'send_notification',
                'result': result
            }

        except Exception as e:
            logger.error(f"发送通知动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _create_task_action(self, db: AsyncSession, project_id: str,
                                params: Dict[str, Any], context_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建任务动作"""
        try:
            task_data = {
                'title': params.get('title', '自动化任务').format(**context_data),
                'description': params.get('description', '').format(**context_data),
                'task_type': params.get('task_type', 'automation_generated'),
                'priority': params.get('priority', 'medium'),
                'metadata': {
                    'automation_generated': True,
                    'context_data': context_data
                }
            }

            result = await self.task_service.create_task(db, task_data, project_id)

            return {
                'success': True,
                'action_type': 'create_task',
                'result': result
            }

        except Exception as e:
            logger.error(f"创建任务动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
