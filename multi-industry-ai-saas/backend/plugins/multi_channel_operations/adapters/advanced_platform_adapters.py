#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级平台适配器
支持更多主流电商和外卖平台
"""

import logging
import asyncio
import json
import httpx
from typing import Dict, List, Any, Optional
from datetime import datetime
from abc import ABC, abstractmethod

import redis.asyncio as redis_client
from ..services.platform_adapters import BasePlatformAdapter

logger = logging.getLogger(__name__)

class JDDaojiaAdapter(BasePlatformAdapter):
    """京东到家适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = "京东到家"
        self.platform_code = "jd_daojia"
        self.api_base_url = "https://api.jddj.com"
    
    async def sync_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步商品到京东到家"""
        try:
            synced_products = []
            failed_products = []
            
            for product in products:
                try:
                    # 转换商品格式
                    jd_product = await self._convert_to_jd_format(product)
                    
                    # 调用京东到家API
                    result = await self._call_jd_api('/product/sync', jd_product)
                    
                    if result.get('success'):
                        synced_products.append({
                            'local_id': product['id'],
                            'platform_id': result['data']['product_id'],
                            'status': 'synced'
                        })
                    else:
                        failed_products.append({
                            'local_id': product['id'],
                            'error': result.get('error', '未知错误')
                        })
                        
                except Exception as e:
                    failed_products.append({
                        'local_id': product['id'],
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'data': {
                    'synced_count': len(synced_products),
                    'failed_count': len(failed_products),
                    'synced_products': synced_products,
                    'failed_products': failed_products
                }
            }
            
        except Exception as e:
            logger.error(f"京东到家商品同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _convert_to_jd_format(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """转换为京东到家格式"""
        return {
            'name': product['name'],
            'description': product.get('description', ''),
            'price': product['price'],
            'stock': product.get('stock', 0),
            'category_id': product.get('category_id'),
            'images': product.get('images', []),
            'attributes': product.get('attributes', {}),
            'sku_list': product.get('variants', [])
        }

class PinduoduoAdapter(BasePlatformAdapter):
    """拼多多适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = "拼多多"
        self.platform_code = "pinduoduo"
        self.api_base_url = "https://gw-api.pinduoduo.com"
    
    async def sync_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步商品到拼多多"""
        try:
            synced_products = []
            failed_products = []
            
            for product in products:
                try:
                    # 转换商品格式
                    pdd_product = await self._convert_to_pdd_format(product)
                    
                    # 调用拼多多API
                    result = await self._call_pdd_api('/goods/add', pdd_product)
                    
                    if result.get('success'):
                        synced_products.append({
                            'local_id': product['id'],
                            'platform_id': result['data']['goods_id'],
                            'status': 'synced'
                        })
                    else:
                        failed_products.append({
                            'local_id': product['id'],
                            'error': result.get('error', '未知错误')
                        })
                        
                except Exception as e:
                    failed_products.append({
                        'local_id': product['id'],
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'data': {
                    'synced_count': len(synced_products),
                    'failed_count': len(failed_products),
                    'synced_products': synced_products,
                    'failed_products': failed_products
                }
            }
            
        except Exception as e:
            logger.error(f"拼多多商品同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

class KuaishouAdapter(BasePlatformAdapter):
    """快手小店适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = "快手小店"
        self.platform_code = "kuaishou"
        self.api_base_url = "https://openapi.kwaixiaodian.com"
    
    async def sync_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步商品到快手小店"""
        try:
            synced_products = []
            failed_products = []
            
            for product in products:
                try:
                    # 转换商品格式
                    ks_product = await self._convert_to_ks_format(product)
                    
                    # 调用快手API
                    result = await self._call_ks_api('/open/v1/product/create', ks_product)
                    
                    if result.get('result') == 1:
                        synced_products.append({
                            'local_id': product['id'],
                            'platform_id': result['data']['productId'],
                            'status': 'synced'
                        })
                    else:
                        failed_products.append({
                            'local_id': product['id'],
                            'error': result.get('error_msg', '未知错误')
                        })
                        
                except Exception as e:
                    failed_products.append({
                        'local_id': product['id'],
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'data': {
                    'synced_count': len(synced_products),
                    'failed_count': len(failed_products),
                    'synced_products': synced_products,
                    'failed_products': failed_products
                }
            }
            
        except Exception as e:
            logger.error(f"快手小店商品同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

class XiaohongshuAdapter(BasePlatformAdapter):
    """小红书适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = "小红书"
        self.platform_code = "xiaohongshu"
        self.api_base_url = "https://ark.xiaohongshu.com"
    
    async def sync_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步商品到小红书"""
        try:
            synced_products = []
            failed_products = []
            
            for product in products:
                try:
                    # 转换商品格式
                    xhs_product = await self._convert_to_xhs_format(product)
                    
                    # 调用小红书API
                    result = await self._call_xhs_api('/api/v1/product/create', xhs_product)
                    
                    if result.get('success'):
                        synced_products.append({
                            'local_id': product['id'],
                            'platform_id': result['data']['product_id'],
                            'status': 'synced'
                        })
                    else:
                        failed_products.append({
                            'local_id': product['id'],
                            'error': result.get('message', '未知错误')
                        })
                        
                except Exception as e:
                    failed_products.append({
                        'local_id': product['id'],
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'data': {
                    'synced_count': len(synced_products),
                    'failed_count': len(failed_products),
                    'synced_products': synced_products,
                    'failed_products': failed_products
                }
            }
            
        except Exception as e:
            logger.error(f"小红书商品同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

class WechatChannelsAdapter(BasePlatformAdapter):
    """微信视频号小店适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = "微信视频号小店"
        self.platform_code = "wechat_channels"
        self.api_base_url = "https://api.weixin.qq.com/channels"
    
    async def sync_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步商品到微信视频号小店"""
        try:
            synced_products = []
            failed_products = []
            
            for product in products:
                try:
                    # 转换商品格式
                    wechat_product = await self._convert_to_wechat_format(product)
                    
                    # 调用微信API
                    result = await self._call_wechat_api('/ec/product/add', wechat_product)
                    
                    if result.get('errcode') == 0:
                        synced_products.append({
                            'local_id': product['id'],
                            'platform_id': result['data']['product_id'],
                            'status': 'synced'
                        })
                    else:
                        failed_products.append({
                            'local_id': product['id'],
                            'error': result.get('errmsg', '未知错误')
                        })
                        
                except Exception as e:
                    failed_products.append({
                        'local_id': product['id'],
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'data': {
                    'synced_count': len(synced_products),
                    'failed_count': len(failed_products),
                    'synced_products': synced_products,
                    'failed_products': failed_products
                }
            }
            
        except Exception as e:
            logger.error(f"微信视频号小店商品同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

class PlatformAdapterFactory:
    """平台适配器工厂"""
    
    _adapters = {
        'meituan': 'MeituanAdapter',
        'douyin': 'DouyinAdapter', 
        'eleme': 'ElemeAdapter',
        'jd_daojia': JDDaojiaAdapter,
        'pinduoduo': PinduoduoAdapter,
        'kuaishou': KuaishouAdapter,
        'xiaohongshu': XiaohongshuAdapter,
        'wechat_channels': WechatChannelsAdapter
    }
    
    @classmethod
    def create_adapter(cls, platform_code: str, config: Dict[str, Any]) -> BasePlatformAdapter:
        """创建平台适配器"""
        adapter_class = cls._adapters.get(platform_code)
        if not adapter_class:
            raise ValueError(f"不支持的平台: {platform_code}")
        
        return adapter_class(config)
    
    @classmethod
    def get_supported_platforms(cls) -> List[Dict[str, Any]]:
        """获取支持的平台列表"""
        return [
            {
                'code': 'meituan',
                'name': '美团外卖',
                'category': 'food_delivery',
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            },
            {
                'code': 'douyin',
                'name': '抖音小店',
                'category': 'social_commerce',
                'features': ['商品管理', '订单管理', '价格同步', '直播带货']
            },
            {
                'code': 'eleme',
                'name': '饿了么',
                'category': 'food_delivery',
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            },
            {
                'code': 'jd_daojia',
                'name': '京东到家',
                'category': 'instant_delivery',
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            },
            {
                'code': 'pinduoduo',
                'name': '拼多多',
                'category': 'group_buying',
                'features': ['商品管理', '订单管理', '价格同步', '团购活动']
            },
            {
                'code': 'kuaishou',
                'name': '快手小店',
                'category': 'social_commerce',
                'features': ['商品管理', '订单管理', '价格同步', '短视频带货']
            },
            {
                'code': 'xiaohongshu',
                'name': '小红书',
                'category': 'social_commerce',
                'features': ['商品管理', '内容营销', '种草推广', 'KOL合作']
            },
            {
                'code': 'wechat_channels',
                'name': '微信视频号小店',
                'category': 'social_commerce',
                'features': ['商品管理', '订单管理', '视频号带货', '微信生态']
            }
        ]

class UnifiedPlatformManager:
    """统一平台管理器"""
    
    def __init__(self):
        self.adapters = {}
        self.cache_key = "platform_adapters"
    
    async def initialize_platforms(self, project_id: str, platform_configs: List[Dict[str, Any]]):
        """初始化平台适配器"""
        try:
            for config in platform_configs:
                platform_code = config['platform_code']
                adapter = PlatformAdapterFactory.create_adapter(platform_code, config)
                self.adapters[platform_code] = adapter
                
                # 缓存适配器配置
                await redis_client.hset(
                    f"{self.cache_key}:{project_id}",
                    platform_code,
                    json.dumps(config)
                )
            
            logger.info(f"已初始化 {len(self.adapters)} 个平台适配器")
            
        except Exception as e:
            logger.error(f"初始化平台适配器失败: {e}")
            raise
    
    async def sync_to_all_platforms(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """同步到所有平台"""
        try:
            results = {}
            
            for platform_code, adapter in self.adapters.items():
                try:
                    result = await adapter.sync_products(products)
                    results[platform_code] = result
                except Exception as e:
                    results[platform_code] = {
                        'success': False,
                        'error': str(e)
                    }
            
            # 统计总体结果
            total_synced = sum(
                r.get('data', {}).get('synced_count', 0) 
                for r in results.values() 
                if r.get('success')
            )
            
            total_failed = sum(
                r.get('data', {}).get('failed_count', 0) 
                for r in results.values() 
                if r.get('success')
            )
            
            return {
                'success': True,
                'data': {
                    'platform_results': results,
                    'summary': {
                        'total_platforms': len(self.adapters),
                        'successful_platforms': len([r for r in results.values() if r.get('success')]),
                        'total_synced': total_synced,
                        'total_failed': total_failed
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"多平台同步失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
