import asyncio
import logging
from sqlalchemy import text
from db.database import AsyncSess<PERSON>Local as SessionLocal  # Use the correct import and alias it

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# The SQL statements are now a list of individual commands
MIGRATION_STATEMENTS = [
    """
    DO $$
    BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'business_modes') THEN
            ALTER TABLE business_modes RENAME TO business_models;
            RAISE NOTICE 'Table business_modes renamed to business_models.';
        END IF;
    END $$;
    """,
    """
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='business_models' AND column_name='parent_id') THEN
            ALTER TABLE business_models ADD COLUMN parent_id UUID REFERENCES business_models(id) ON DELETE SET NULL;
            RAISE NOTICE 'Column parent_id added to business_models.';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='business_models' AND column_name='description') THEN
            ALTER TABLE business_models ADD COLUMN description TEXT;
            RAISE NOTICE 'Column description added to business_models.';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='business_models' AND column_name='type') THEN
            ALTER TABLE business_models ADD COLUMN type VARCHAR(20) NOT NULL DEFAULT 'debit';
            RAISE NOTICE 'Column type added to business_models.';
        END IF;
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='business_models' AND column_name='status') THEN
            ALTER TABLE business_models ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'active';
            RAISE NOTICE 'Column status added to business_models.';
        END IF;
    END $$;
    """,
    """
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name='business_models' AND constraint_name='uq_business_models_project_id_code') THEN
            -- First, drop the old constraint if it exists on the old table name, just in case
            IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name='business_modes' AND constraint_name='business_modes_project_id_code_key') THEN
                ALTER TABLE business_models DROP CONSTRAINT IF EXISTS business_modes_project_id_code_key;
            END IF;
            ALTER TABLE business_models ADD CONSTRAINT uq_business_models_project_id_code UNIQUE (project_id, code);
            RAISE NOTICE 'Constraint uq_business_models_project_id_code added.';
        END IF;
    END $$;
    """,
    "CREATE INDEX IF NOT EXISTS idx_business_models_parent_id ON business_models(parent_id);",
    "CREATE INDEX IF NOT EXISTS idx_business_models_project_id ON business_models(project_id);",
    """
    CREATE TABLE IF NOT EXISTS sales_report_business_details (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        sales_report_id UUID NOT NULL REFERENCES sales_reports(id) ON DELETE CASCADE,
        business_model_id UUID NOT NULL REFERENCES business_models(id) ON DELETE CASCADE,
        amount FLOAT NOT NULL DEFAULT 0.0,
        count INTEGER NOT NULL DEFAULT 0,
        details JSONB,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    """,
    "CREATE INDEX IF NOT EXISTS idx_srbd_report_id ON sales_report_business_details(sales_report_id);",
    "CREATE INDEX IF NOT EXISTS idx_srbd_model_id ON sales_report_business_details(business_model_id);",
    "ALTER TABLE sales_reports DROP COLUMN IF EXISTS recharge_amount;",
    "ALTER TABLE sales_reports DROP COLUMN IF EXISTS card_sales_amount;",
    "ALTER TABLE sales_reports DROP COLUMN IF EXISTS recharge_count;",
    "ALTER TABLE sales_reports DROP COLUMN IF EXISTS card_sales_count;",
    "ALTER TABLE sales_reports DROP COLUMN IF EXISTS recharge_sales_data;"
]

async def main():
    logger.info("Starting database migration...")
    db = SessionLocal()
    try:
        async with db.begin(): # Start a transaction
            for i, statement in enumerate(MIGRATION_STATEMENTS):
                logger.info(f"Executing statement {i+1}/{len(MIGRATION_STATEMENTS)}...")
                await db.execute(text(statement))
                logger.info(f"Statement {i+1} executed successfully.")
        logger.info("Migration transaction committed successfully.")
    except Exception as e:
        logger.error(f"An error occurred during migration: {e}", exc_info=True)
        # The transaction will be rolled back automatically by the `async with` block
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(main()) 