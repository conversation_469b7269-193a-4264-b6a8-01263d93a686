from sqlalchemy import Column, String, Float, Boolean, ForeignKey, Text, JSON, Enum as DBEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
import enum
from datetime import datetime
from .base import TimestampMixin
from db.database import Base
from .finance import FinancialReconciliation

# --- 新增的枚举类型 ---
class ChannelNature(str, enum.Enum):
    ONLINE = "online"
    OFFLINE = "offline"
    HYBRID = "hybrid"

# --- 新增的基础定义模型 ---

class ChannelPlatform(Base, TimestampMixin):
    """层级1: 渠道平台 (e.g., 美团, 抖音)"""
    __tablename__ = 'channel_platforms'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True, comment="平台名称")
    code = Column(String(50), nullable=False, unique=True, comment="平台编码")
    nature = Column(DBEnum(ChannelNature, name="channel_nature"), nullable=False, comment="平台性质")
    logo_url = Column(String(255), nullable=True, comment="平台Logo URL")
    
    services = relationship("PlatformService", back_populates="platform", cascade="all, delete-orphan")

class PlatformService(Base, TimestampMixin):
    """层级2: 平台提供的服务 (e.g., 美团外卖, 抖音团购)"""
    __tablename__ = 'platform_services'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform_id = Column(UUID(as_uuid=True), ForeignKey('channel_platforms.id'), nullable=False)
    name = Column(String(100), nullable=False, comment="服务名称")
    code = Column(String(50), nullable=False, comment="服务编码")
    
    platform = relationship("ChannelPlatform", back_populates="services")
    instances = relationship("SalesChannel", back_populates="service")

class BusinessModel(Base, TimestampMixin):
    """公司的业务模式 (e.g., 到店消费, 配送上门, 会员充值)"""
    __tablename__ = 'business_models'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)
    
    name = Column(String(100), nullable=False, comment="业务模式名称")
    code = Column(String(50), nullable=False, unique=True, comment="业务模式编码 (项目内唯一)")
    
    parent_id = Column(UUID(as_uuid=True), ForeignKey('business_models.id'), nullable=True, index=True, comment="父级业务模式ID")
    description = Column(Text, nullable=True, comment="详细描述")
    
    # "debit" for revenue-generating activities (sales), 
    # "credit" for liability-generating activities (recharges)
    type = Column(String(20), nullable=False, default='debit', comment="业务类型 (debit/credit)")
    
    status = Column(String(20), default='active', nullable=False, comment="状态 (active/inactive)")

    project = relationship("Project")
    parent = relationship('BusinessModel', remote_side=[id], back_populates='children')
    children = relationship('BusinessModel', back_populates='parent')

# --- 关联表 ---
class SalesChannelPaymentMethod(Base):
    __tablename__ = 'sales_channel_payment_methods'
    sales_channel_id = Column(UUID(as_uuid=True), ForeignKey('sales_channels.id', ondelete="CASCADE"), primary_key=True)
    payment_method_id = Column(UUID(as_uuid=True), ForeignKey('payment_methods.id', ondelete="CASCADE"), primary_key=True)


# --- 重构后的核心模型 ---

class SalesChannel(Base, TimestampMixin):
    """层级3: 项目中实际的销售渠道实例"""
    __tablename__ = "sales_channels"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="SET NULL"), nullable=True, index=True, comment="关联的门店ID")
    
    custom_name = Column(String(200), nullable=False, comment="渠道实例的自定义名称, e.g., '上海总店美团外卖'")
    service_id = Column(UUID(as_uuid=True), ForeignKey('platform_services.id'), nullable=False)
    business_mode_id = Column(UUID(as_uuid=True), ForeignKey('business_models.id'), nullable=False)

    is_active = Column(Boolean, default=True, comment="是否启用")
    config = Column(JSON, nullable=True, comment="实例特定配置, e.g., API keys, store_id")
    description = Column(Text, nullable=True, comment="渠道描述")

    project = relationship("Project", back_populates="sales_channels")
    store = relationship("Store")
    service = relationship("PlatformService", back_populates="instances")
    business_mode = relationship("BusinessModel")
    
    # 建立与支付方式的多对多关系
    payment_methods = relationship("PaymentMethod", secondary="sales_channel_payment_methods", back_populates="sales_channels")

    # financial_reconciliations 的关系保持不变
    financial_reconciliations = relationship("FinancialReconciliation", back_populates="channel")

class PaymentMethod(Base, TimestampMixin):
    """支付方式模型"""
    __tablename__ = "payment_methods"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False, comment="支付方式名称")
    type = Column(String(50), nullable=True, comment="支付类型 (e.g., online, cash, third-party)")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为项目默认")
    config = Column(JSON, nullable=True, comment="支付方式配置")

    project = relationship("Project", back_populates="payment_methods")
    
    # 建立与销售渠道的多对多关系
    sales_channels = relationship("SalesChannel", secondary="sales_channel_payment_methods", back_populates="payment_methods")

# --- 模板相关模型 ---

class ChannelTemplateInstancePaymentMethod(Base):
    __tablename__ = 'channel_template_instance_payment_methods'
    instance_id = Column(UUID(as_uuid=True), ForeignKey('channel_template_instances.id', ondelete="CASCADE"), primary_key=True)
    payment_method_id = Column(UUID(as_uuid=True), ForeignKey('payment_methods.id', ondelete="CASCADE"), primary_key=True)

class ChannelTemplateInstance(Base, TimestampMixin):
    """渠道模板中的单个配置实例"""
    __tablename__ = 'channel_template_instances'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey('sales_channel_templates.id', ondelete="CASCADE"), nullable=False)
    
    service_id = Column(UUID(as_uuid=True), ForeignKey('platform_services.id'), nullable=False)
    business_mode_id = Column(UUID(as_uuid=True), ForeignKey('business_models.id'), nullable=False)

    service = relationship("PlatformService")
    business_mode = relationship("BusinessModel")
    template = relationship("SalesChannelTemplate", back_populates="instances")
    payment_methods = relationship(
        "PaymentMethod",
        secondary='channel_template_instance_payment_methods'
    )

class SalesChannelTemplate(Base, TimestampMixin):
    """后台管理的渠道实例模板, 现在包含多个独立的实例配置"""
    __tablename__ = 'sales_channel_templates'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)
    
    name = Column(String(100), nullable=False, comment="模板名称, e.g., '标准外卖模板'")
    description = Column(Text, nullable=True, comment="模板描述")
    
    project = relationship("Project")
    
    instances = relationship(
        "ChannelTemplateInstance", 
        back_populates="template", 
        cascade="all, delete-orphan"
    )
