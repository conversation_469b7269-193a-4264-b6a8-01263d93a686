#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from sqlalchemy import delete, or_, and_, func, desc, asc, text, case, Float
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload
from collections import defaultdict

from models.sales_report import SalesReport, SalesReportItem, SalesReportChannel, SalesReportStatus, SalesReportType, SalesReportBusinessDetail
from models.store import Store, StoreRegion
from models.sales_management import SalesChannel, PaymentMethod, BusinessModel
from models.user import User
from schemas.sales_report import SalesReportCreate, SalesReportUpdate, SalesReportStatusUpdate

logger = logging.getLogger(__name__)

class SalesReportService:
    @staticmethod
    async def get_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sort_by: str = "report_date",
        sort_order: str = "desc"
    ) -> List[SalesReport]:
        """获取销售上报列表"""
        query = select(SalesReport).where(SalesReport.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(SalesReport.store_id == store_id)
        if status:
            try:
                status_enum = SalesReportStatus(status.lower())
                query = query.where(SalesReport.status == status_enum)
            except ValueError:
                logger.warning(f"提供了无效的销售报告状态 '{status}'，已忽略该过滤器。")
        if report_type:
            try:
                report_type_enum = SalesReportType(report_type.lower())
                query = query.where(SalesReport.report_type == report_type_enum)
            except ValueError:
                logger.warning(f"提供了无效的销售报告类型 '{report_type}'，已忽略该过滤器。")
        if search:
            query = query.where(SalesReport.notes.ilike(f"%{search}%"))
        if start_date:
            query = query.where(SalesReport.report_date >= start_date)
        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        # 应用排序
        order_column = getattr(SalesReport, sort_by, SalesReport.report_date)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))

        # 加载关联数据
        query = query.options(
            joinedload(SalesReport.store).options(
                selectinload(Store.category),
                selectinload(Store.region).joinedload(StoreRegion.parent)
            ),
            selectinload(SalesReport.items),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver),
            selectinload(SalesReport.channel_details).joinedload(SalesReportChannel.sales_channel),
            selectinload(SalesReport.business_details).joinedload(SalesReportBusinessDetail.business_model)
        )

        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return list(result.unique().scalars().all())

    @staticmethod
    async def count_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> int:
        """计算销售上报数量"""
        query = select(func.count(SalesReport.id)).where(SalesReport.project_id == project_id)

        if store_id:
            query = query.where(SalesReport.store_id == store_id)
        if status:
            try:
                status_enum = SalesReportStatus(status.lower())
                query = query.where(SalesReport.status == status_enum)
            except ValueError:
                logger.warning(f"提供了无效的销售报告状态 '{status}'，已忽略该过滤器。")
        if report_type:
            try:
                report_type_enum = SalesReportType(report_type.lower())
                query = query.where(SalesReport.report_type == report_type_enum)
            except ValueError:
                logger.warning(f"提供了无效的销售报告类型 '{report_type}'，已忽略该过滤器。")
        if search:
            query = query.where(SalesReport.notes.ilike(f"%{search}%"))
        if start_date:
            query = query.where(SalesReport.report_date >= start_date)
        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_report_by_id(db: AsyncSession, report_id: uuid.UUID) -> Optional[SalesReport]:
        result = await db.execute(select(SalesReport).where(SalesReport.id == report_id))
        return result.scalars().first()

    @staticmethod
    async def get_report_with_details(db: AsyncSession, report_id: uuid.UUID) -> Optional[SalesReport]:
        """获取带有完整详情的单一报告"""
        query = select(SalesReport).where(SalesReport.id == report_id).options(
            joinedload(SalesReport.store).options(
                selectinload(Store.category),
                selectinload(Store.region).joinedload(StoreRegion.parent)
            ),
            selectinload(SalesReport.items),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver),
            selectinload(SalesReport.channel_details).joinedload(SalesReportChannel.sales_channel),
            selectinload(SalesReport.business_details).joinedload(SalesReportBusinessDetail.business_model)
        )
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_report(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        report_data: SalesReportCreate
    ) -> SalesReport:
        """创建新的销售报告 (已重构为业务模式驱动)"""
        
        # Validation logic remains the same...
        existing_report_query = select(SalesReport).where(
            SalesReport.store_id == report_data.store_id,
            func.date(SalesReport.report_date) == report_data.report_date
        )
        existing_reports_result = await db.execute(existing_report_query)
        existing_reports = existing_reports_result.scalars().all()

        if existing_reports:
            is_daily_creating = report_data.report_type == 'daily'
            has_shift_report = any(r.report_type == 'shift' for r in existing_reports)
            has_daily_report = any(r.report_type == 'daily' for r in existing_reports)

            if is_daily_creating and has_shift_report:
                raise ValueError("该日期已存在班次上报，无法创建日报。日报数据由班次上报自动汇总。")
            if not is_daily_creating and has_daily_report:
                raise ValueError("该日期已存在日报，无法再创建班次上报。")
            if is_daily_creating and has_daily_report:
                raise ValueError("该日期已存在日报，无法重复创建。")

        # Calculate totals from channel details (sales revenue)
        total_sales = sum(
            pm.amount 
            for cd in report_data.channel_details 
            for pm in cd.payment_methods_details
        )
        total_orders = sum(
            pm.orders 
            for cd in report_data.channel_details 
            for pm in cd.payment_methods_details
        )
        
        # Create the main report object without deprecated fields
        db_report = SalesReport(
            project_id=project_id,
            store_id=report_data.store_id,
            reporter_id=user_id,
            created_by=user_id,
            updated_by=user_id,
            report_date=datetime.combine(report_data.report_date, datetime.min.time()),
            report_type=report_data.report_type,
            notes=report_data.notes,
            status='draft',
            total_sales=total_sales, # This is now purely sales from channels
            total_orders=total_orders,
            # Deprecated fields are no longer populated here
        )
        db.add(db_report)
        await db.flush()

        # Create channel details (this part remains largely the same)
        for channel_detail_data in report_data.channel_details:
            channel_total_sales = sum(pm.amount for pm in channel_detail_data.payment_methods_details)
            channel_total_orders = sum(pm.orders for pm in channel_detail_data.payment_methods_details)

            db_channel_detail = SalesReportChannel(
                sales_report_id=db_report.id,
                sales_channel_id=channel_detail_data.sales_channel_id,
                total_sales=channel_total_sales,
                total_orders=channel_total_orders,
                payment_methods_details=[pm.model_dump() for pm in channel_detail_data.payment_methods_details]
            )
            db.add(db_channel_detail)

        # Create business model details
        for business_detail_data in report_data.business_details:
            db_business_detail = SalesReportBusinessDetail(
                sales_report_id=db_report.id,
                **business_detail_data.model_dump()
            )
            db.add(db_business_detail)

        # Create item details
        for item_data in report_data.items:
            total_amount = item_data.quantity * item_data.unit_price - item_data.discount_amount
            db_item = SalesReportItem(
                sales_report_id=db_report.id,
                total_amount=total_amount,
                **item_data.model_dump()
            )
            db.add(db_item)

        await db.commit()
        await db.refresh(db_report)
        return await SalesReportService.get_report_with_details(db, db_report.id)

    @staticmethod
    async def update_report(
        db: AsyncSession,
        report_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        report_data: SalesReportUpdate
    ) -> Optional[SalesReport]:
        """更新销售报告 (已重构为业务模式驱动)"""
        db_report = await db.get(SalesReport, report_id)
        if not db_report or db_report.project_id != project_id:
            return None

        update_data = report_data.model_dump(exclude_unset=True)

        # Update simple fields
        for key, value in update_data.items():
            if hasattr(db_report, key) and key not in ["channel_details", "business_details", "items"]:
                setattr(db_report, key, value)
        db_report.updated_by = user_id
        
        # If channel details are provided, delete old ones and create new ones
        if 'channel_details' in update_data:
            await db.execute(delete(SalesReportChannel).where(SalesReportChannel.sales_report_id == report_id))
            for channel_detail_data in report_data.channel_details:
                # ... creation logic from create_report ...
                db.add(SalesReportChannel(sales_report_id=report_id, **channel_detail_data.model_dump()))
            # Recalculate totals
            db_report.total_sales = sum(cd.amount for cd in report_data.channel_details)
            db_report.total_orders = sum(cd.orders for cd in report_data.channel_details)

        # If business details are provided, delete old ones and create new ones
        if 'business_details' in update_data:
            await db.execute(delete(SalesReportBusinessDetail).where(SalesReportBusinessDetail.sales_report_id == report_id))
            for detail_data in report_data.business_details:
                db.add(SalesReportBusinessDetail(sales_report_id=report_id, **detail_data.model_dump()))
        
        # If item details are provided, delete old ones and create new ones
        if 'items' in update_data:
            await db.execute(delete(SalesReportItem).where(SalesReportItem.sales_report_id == report_id))
            for item_data in report_data.items:
                # ... creation logic from create_report ...
                db.add(SalesReportItem(sales_report_id=report_id, **item_data.model_dump()))

        await db.commit()
        await db.refresh(db_report)
        return await SalesReportService.get_report_with_details(db, db_report.id)

    @staticmethod
    async def update_sales_report_status(
        db: AsyncSession,
        report_id: uuid.UUID,
        status_data: SalesReportStatusUpdate,
        user_id: uuid.UUID
    ) -> Optional[SalesReport]:
        report = await db.get(SalesReport, report_id)
        if not report:
            return None

        report.status = status_data.status
        report.updated_by = user_id
        if status_data.status == 'published':
            report.approved_by = user_id
            report.approved_at = datetime.utcnow()
        elif status_data.status == 'rejected': # Assuming a 'rejected' status might be added
            report.reject_reason = status_data.notes
        
        await db.commit()
        await db.refresh(report)
        return report
    
    @staticmethod
    async def get_sales_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取销售统计数据 (业务模式驱动)"""
        
        # Base query for reports in the date range
        report_query = select(SalesReport.id).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date.between(start_date, end_date),
            SalesReport.status == 'published'
        )
        if store_id:
            report_query = report_query.where(SalesReport.store_id == store_id)
        
        report_ids = (await db.execute(report_query)).scalars().all()
        if not report_ids:
            return {
                "total_sales": 0, "total_orders": 0, "total_recharge_amount": 0,
                "total_recharge_orders": 0, "grand_total_amount": 0, "grand_total_orders": 0,
                "channel_summary": [], "payment_method_summary": [], "business_model_summary": []
            }

        # 1. Sales from channels
        channel_sales_query = select(
            func.sum(SalesReportChannel.total_sales).label("total_sales"),
            func.sum(SalesReportChannel.total_orders).label("total_orders")
        ).where(SalesReportChannel.sales_report_id.in_(report_ids))
        sales_result = (await db.execute(channel_sales_query)).first()
        
        total_sales = sales_result.total_sales or 0
        total_orders = sales_result.total_orders or 0

        # 2. Aggregations from business details
        business_details_query = select(
            BusinessModel.type,
            func.sum(SalesReportBusinessDetail.amount).label("total_amount"),
            func.sum(SalesReportBusinessDetail.count).label("total_count")
        ).select_from(SalesReportBusinessDetail).join(BusinessModel).where(
            SalesReportBusinessDetail.sales_report_id.in_(report_ids)
        ).group_by(BusinessModel.type)
        
        business_details_results = (await db.execute(business_details_query)).all()
        
        total_recharge_amount = 0
        total_recharge_orders = 0
        for res in business_details_results:
            if res.type == 'credit': # Assuming 'credit' is for recharges
                total_recharge_amount += res.total_amount or 0
                total_recharge_orders += res.total_count or 0
        
        # 3. Channel Summary
        channel_summary_query = select(
            SalesChannel.custom_name.label("channel_name"),
            func.sum(SalesReportChannel.total_sales).label("total_sales")
        ).select_from(SalesReportChannel).join(SalesChannel).where(
            SalesReportChannel.sales_report_id.in_(report_ids)
        ).group_by(SalesChannel.custom_name).order_by(desc("total_sales"))
        
        channel_summary = (await db.execute(channel_summary_query)).mappings().all()

        # 4. Business Model Summary
        business_model_summary_query = select(
            BusinessModel.name.label("business_model_name"),
            BusinessModel.type.label("business_model_type"),
            func.sum(SalesReportBusinessDetail.amount).label("total_amount")
        ).select_from(SalesReportBusinessDetail).join(BusinessModel).where(
            SalesReportBusinessDetail.sales_report_id.in_(report_ids)
        ).group_by(BusinessModel.name, BusinessModel.type).order_by(desc("total_amount"))
        
        business_model_summary = (await db.execute(business_model_summary_query)).mappings().all()
        
        # Payment method summary would require expanding the JSONB field, which can be complex.
        # For now, returning an empty list.
        payment_method_summary = []

        return {
            "total_sales": total_sales,
            "total_orders": total_orders,
            "total_recharge_amount": total_recharge_amount,
            "total_recharge_orders": total_recharge_orders,
            "grand_total_amount": total_sales + total_recharge_amount,
            "grand_total_orders": total_orders + total_recharge_orders,
            "channel_summary": channel_summary,
            "payment_method_summary": payment_method_summary,
            "business_model_summary": business_model_summary
        }

    @staticmethod
    async def aggregate_daily_report(db: AsyncSession, project_id: uuid.UUID, store_id: uuid.UUID, report_date: date, user_id: uuid.UUID):
        """根据当天的所有班次报告，创建或更新日报"""
        start_of_day = datetime.combine(report_date, datetime.min.time())
        end_of_day = datetime.combine(report_date, datetime.max.time())

        # 查找当天的所有班次报告
        stmt = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            SalesReport.report_date >= start_of_day,
            SalesReport.report_date <= end_of_day,
            SalesReport.report_type == 'shift'
        )
        result = await db.execute(stmt)
        shift_reports = result.scalars().all()

        # 查找是否已存在日报
        daily_report_stmt = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            func.date(SalesReport.report_date) == report_date,
            SalesReport.report_type == 'daily'
        )
        existing_daily_report = (await db.execute(daily_report_stmt)).scalars().first()

        if not shift_reports:
            # 如果没有班次报告了，但日报还存在，则删除日报
            if existing_daily_report:
                await db.delete(existing_daily_report)
                await db.commit()
            return

        # 初始化汇总数据
        total_sales, total_orders, total_customers, online_sales, offline_sales = 0, 0, 0, 0, 0
        recharge_amount, card_sales_amount, recharge_count, card_sales_count = 0, 0, 0, 0
        payment_methods, recharge_payment_methods = {}, {}
        
        for report in shift_reports:
            total_sales += report.total_sales
            total_orders += report.total_orders
            total_customers += report.total_customers
            online_sales += report.online_sales
            offline_sales += report.offline_sales
            recharge_amount += report.recharge_amount
            card_sales_amount += report.card_sales_amount
            recharge_count += report.recharge_count
            card_sales_count += report.card_sales_count
            
            for method, amount in (report.payment_methods or {}).items():
                payment_methods[method] = payment_methods.get(method, 0) + amount
            for method, amount in (report.recharge_payment_methods or {}).items():
                recharge_payment_methods[method] = recharge_payment_methods.get(method, 0) + amount

        if existing_daily_report:
            # 更新现有日报
            daily_report = existing_daily_report
            daily_report.updated_by = user_id
            daily_report.updated_at = datetime.utcnow()
        else:
            # 创建新日报
            daily_report = SalesReport(
                project_id=project_id,
                store_id=store_id,
                report_date=start_of_day, # 使用日期开始时间
                report_type='daily',
                status='submitted',
                created_by=user_id,
                updated_by=user_id,
            )
            db.add(daily_report)
        
        # 填充数据
        daily_report.total_sales = total_sales
        daily_report.total_orders = total_orders
        daily_report.total_customers = total_customers
        daily_report.online_sales = online_sales
        daily_report.offline_sales = offline_sales
        daily_report.payment_methods = payment_methods
        daily_report.recharge_amount = recharge_amount
        daily_report.card_sales_amount = card_sales_amount
        daily_report.recharge_count = recharge_count
        daily_report.card_sales_count = card_sales_count
        daily_report.recharge_payment_methods = recharge_payment_methods
        
        await db.commit()

async def get_sales_report_summary(db: AsyncSession, project_id: uuid.UUID, start_date: date, end_date: date) -> Dict[str, Any]:
    """
    获取销售报告摘要，用于仪表盘。
    """
    query = (
        select(
            func.sum(SalesReport.total_sales).label("total_revenue"),
            func.sum(SalesReport.total_orders).label("total_orders"),
            func.sum(SalesReport.total_customers).label("total_customers"),
            func.sum(case((SalesChannel.is_online, SalesReportChannel.total_sales)), else_=0).label("online_revenue"),
            func.sum(case((~SalesChannel.is_online, SalesReportChannel.total_sales)), else_=0).label("offline_revenue"),
        )
        .join(SalesReport.channel_details)
        .join(SalesReportChannel.sales_channel)
        .where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date,
            SalesReport.status == "published"
        )
    )
    
    result = await db.execute(query)
    summary = result.first()
    
    return {
        "total_revenue": summary.total_revenue or 0,
        "total_orders": summary.total_orders or 0,
        "total_customers": summary.total_customers or 0,
        "online_revenue": summary.online_revenue or 0,
        "offline_revenue": summary.offline_revenue or 0,
    }
