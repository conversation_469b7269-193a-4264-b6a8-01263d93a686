import React, { useState, useEffect, useCallback } from 'react';
import { Card, Tabs, Table, Button, Space, Modal, Form, Input, Select, Switch, message, Popconfirm, Tag, Typography } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import salesManagementApi from '../../../../services/api/project/salesManagement';
import ChannelTemplateManagement from './ChannelTemplateManagement';

const { TabPane } = Tabs;
const { Option } = Select;
const { Title } = Typography;

// --- Manager Components ---

const PlatformManager = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [form] = Form.useForm();

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const response = await salesManagementApi.getPlatforms();
            // 平台API返回直接数组，不是{items: [...]}格式
            setData(Array.isArray(response) ? response : (response.items || []));
        } catch (error) {
            message.error('获取平台列表失败');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleEdit = (item) => {
        setEditingItem(item);
        form.setFieldsValue(item);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingItem(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            if (editingItem) {
                await salesManagementApi.updatePlatform(editingItem.id, values);
                message.success('更新成功');

            } else {
                await salesManagementApi.createPlatform(values);
                message.success('创建成功');
            }
            setModalVisible(false);
            fetchData();
        } catch (error) {
            message.error('操作失败');
        }
    };
    
    const handleDelete = async (id) => {
        try {
            await salesManagementApi.deletePlatform(id);
            message.success('删除成功');
            fetchData();
        } catch (error) {
            message.error('删除失败');
        }
    };
    
    const columns = [
        { title: '平台名称', dataIndex: 'name', key: 'name' },
        { title: '平台编码', dataIndex: 'code', key: 'code' },
        { title: '平台性质', dataIndex: 'nature', key: 'nature', render: (nature) => <Tag>{nature}</Tag> },
        { title: '操作', key: 'action', render: (_, record) => (
            <Space>
                <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
                <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record.id)}>
                    <Button type="link" danger>删除</Button>
                </Popconfirm>
            </Space>
        )}
    ];
      
    return (
        <>
            <Button icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>添加平台</Button>
            <Table rowKey="id" dataSource={data} columns={columns} loading={loading} />
            <Modal title={editingItem ? '编辑平台' : '添加平台'} open={modalVisible} onOk={handleOk} onCancel={() => setModalVisible(false)}>
                <Form form={form} layout="vertical">
                    <Form.Item name="name" label="平台名称" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="code" label="平台编码" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="nature" label="平台性质" rules={[{ required: true }]}>
                        <Select>
                            <Option value="ONLINE">线上</Option>
                            <Option value="OFFLINE">线下</Option>
                            <Option value="THIRD_PARTY">三方</Option>
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

const ServiceManager = () => {
    const [data, setData] = useState([]);
    const [platforms, setPlatforms] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [form] = Form.useForm();

    const fetchPlatforms = useCallback(async () => {
      try {
        const response = await salesManagementApi.getPlatforms();
        // 平台API返回直接数组，不是{items: [...]}格式
        setPlatforms(Array.isArray(response) ? response : (response.items || []));
      } catch (error) {
        message.error('获取平台列表失败');
      }
    }, []);

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const response = await salesManagementApi.getAllServices();
            // 服务API返回直接数组，不是{items: [...]}格式
            setData(Array.isArray(response) ? response : (response.items || []));
        } catch (error) {
            message.error('获取服务列表失败');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        fetchPlatforms();
    }, [fetchData, fetchPlatforms]);

    const handleEdit = (item) => {
        setEditingItem(item);
        form.setFieldsValue(item);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingItem(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            if (editingItem) {
                await salesManagementApi.updateService(editingItem.id, values);
                message.success('更新成功');
            } else {
                await salesManagementApi.createService(values);
                message.success('创建成功');
            }
            setModalVisible(false);
            fetchData();
        } catch (error) {
            message.error('操作失败');
        }
    };

    const handleDelete = async (id) => {
        try {
            await salesManagementApi.deleteService(id);
            message.success('删除成功');
            fetchData();
        } catch (error) {
            message.error('删除失败');
        }
    };

    const columns = [
        { title: '服务名称', dataIndex: 'name', key: 'name' },
        { title: '服务编码', dataIndex: 'code', key: 'code' },
        { title: '所属平台', dataIndex: ['platform', 'name'], key: 'platform' },
        { title: '操作', key: 'action', render: (_, record) => (
            <Space>
                <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
                <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record.id)}>
                    <Button type="link" danger>删除</Button>
                </Popconfirm>
            </Space>
        )}
    ];
     
    return (
      <>
        <Button icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>添加服务</Button>
        <Table rowKey="id" dataSource={data} columns={columns} loading={loading} />
        <Modal title={editingItem ? '编辑服务' : '添加服务'} open={modalVisible} onOk={handleOk} onCancel={() => setModalVisible(false)}>
            <Form form={form} layout="vertical">
                <Form.Item name="platform_id" label="所属平台" rules={[{ required: true }]}>
                    <Select>
                        {platforms.map(p => <Option key={p.id} value={p.id}>{p.name}</Option>)}
                    </Select>
                </Form.Item>
                <Form.Item name="name" label="服务名称" rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item name="code" label="服务编码" rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
      </>
    );
};
  
const BusinessModeManager = () => {
    const [allModes, setAllModes] = useState([]); // Flat list for dropdowns
    const [treeData, setTreeData] = useState([]); // Tree data for table
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [form] = Form.useForm();

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            // Use the existing imported API object
            const response = await salesManagementApi.getBusinessModes({ limit: 500 });
            const modes = Array.isArray(response) ? response : (response.items || []);
            setAllModes(modes);

            // Helper to build tree data
            const buildTree = (list) => {
                const map = {};
                const roots = [];
                if (!Array.isArray(list)) return roots;

                list.forEach(item => {
                    map[item.id] = { ...item, children: [] };
                });
                list.forEach(item => {
                    if (item.parent_id && map[item.parent_id]) {
                        map[item.parent_id].children.push(map[item.id]);
                    } else {
                        roots.push(map[item.id]);
                    }
                });
                // If a child has an empty children array, remove it for cleaner presentation
                list.forEach(item => {
                    if (map[item.id]?.children.length === 0) {
                        delete map[item.id].children;
                    }
                });
                return roots;
            };

            setTreeData(buildTree(modes));
        } catch (error) {
            message.error('获取业务模式列表失败');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleEdit = (item) => {
        setEditingItem(item);
        form.setFieldsValue({
            ...item,
            parent_id: item.parent_id || null, 
        });
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingItem(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            const payload = { ...values };
            if (!payload.parent_id) {
                payload.parent_id = null;
            }

            if (editingItem) {
                await salesManagementApi.updateBusinessMode(editingItem.id, payload);
                message.success('更新成功');
            } else {
                await salesManagementApi.createBusinessMode(payload);
                message.success('创建成功');
            }
            setModalVisible(false);
            fetchData();
        } catch (error) {
            const errorMsg = error.response?.data?.detail || '操作失败';
            message.error(errorMsg);
        }
    };
    
    const handleDelete = async (id) => {
        try {
            await salesManagementApi.deleteBusinessMode(id);
            message.success('删除成功');
            fetchData();
        } catch (error) {
            const errorMsg = error.response?.data?.detail || '删除失败';
            message.error(errorMsg);
        }
    };
    
    const columns = [
        { title: '模式名称', dataIndex: 'name', key: 'name' },
        { title: '模式编码', dataIndex: 'code', key: 'code', width: 150 },
        { 
            title: '业务类型', 
            dataIndex: 'type', 
            key: 'type',
            width: 120,
            render: (type) => type === 'debit' 
                ? <Tag color="green">收入型</Tag> 
                : <Tag color="orange">负债型</Tag> 
        },
        { 
            title: '状态', 
            dataIndex: 'status', 
            key: 'status',
            width: 100,
            render: (status) => <Tag color={status === 'active' ? 'blue' : 'grey'}>{status === 'active' ? '启用' : '禁用'}</Tag> 
        },
        { title: '描述', dataIndex: 'description', key: 'description' },
        { title: '操作', key: 'action', width: 150, fixed: 'right', render: (_, record) => (
            <Space>
                <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
                <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record.id)}>
                    <Button type="link" danger>删除</Button>
                </Popconfirm>
            </Space>
        )}
    ];
     
    return (
      <>
        <Button icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>添加业务模式</Button>
        <Table 
            rowKey="id" 
            dataSource={treeData} 
            columns={columns} 
            loading={loading}
            expandable={{
                defaultExpandAllRows: true,
            }}
            scroll={{ x: 1000 }}
        />
        <Modal 
            title={editingItem ? '编辑业务模式' : '添加业务模式'} 
            open={modalVisible} 
            onOk={handleOk} 
            onCancel={() => setModalVisible(false)}
            destroyOnClose
        >
            <Form form={form} layout="vertical" initialValues={{ status: 'active', type: 'debit' }}>
                <Form.Item name="name" label="模式名称" rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item name="code" label="模式编码" rules={[{ required: true }]}>
                    <Input />
                </Form.Item>
                <Form.Item name="parent_id" label="父级模式">
                    <Select showSearch allowClear placeholder="可选，作为顶级模式请留空">
                        {allModes
                          .filter(item => !editingItem || item.id !== editingItem.id)
                          .map(p => <Option key={p.id} value={p.id}>{p.name}</Option>)}
                    </Select>
                </Form.Item>
                <Form.Item name="type" label="业务类型" rules={[{ required: true }]}>
                    <Select>
                        <Option value="debit">收入型 (例如: 商品销售, 服务费)</Option>
                        <Option value="credit">负债型 (例如: 会员充值, 售卡)</Option>
                    </Select>
                </Form.Item>
                <Form.Item name="status" label="状态" rules={[{ required: true }]}>
                    <Select>
                        <Option value="active">启用</Option>
                        <Option value="inactive">禁用</Option>
                    </Select>
                </Form.Item>
                <Form.Item name="description" label="描述">
                    <Input.TextArea rows={2} />
                </Form.Item>
            </Form>
        </Modal>
      </>
    );
};

const PaymentMethodManager = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [form] = Form.useForm();

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const response = await salesManagementApi.getPaymentMethods();
            setData(response.items || []);
        } catch (error) {
            message.error('获取支付方式列表失败');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleEdit = (item) => {
        setEditingItem(item);
        form.setFieldsValue(item);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingItem(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            if (editingItem) {
                await salesManagementApi.updatePaymentMethod(editingItem.id, values);
                message.success('更新成功');
            } else {
                await salesManagementApi.createPaymentMethod(values);
                message.success('创建成功');
            }
            setModalVisible(false);
            fetchData();
        } catch (error) {
            message.error('操作失败');
        }
    };

    const handleDelete = async (id) => {
        try {
            await salesManagementApi.deletePaymentMethod(id);
            message.success('删除成功');
            fetchData();
        } catch (error) {
            message.error('删除失败');
        }
    };

    const columns = [
        { title: '名称', dataIndex: 'name', key: 'name' },
        { title: '类型', dataIndex: 'type', key: 'type', render: (t) => t || '-' },
        { title: '状态', dataIndex: 'is_active', key: 'is_active', render: active => <Tag color={active ? 'green' : 'red'}>{active ? '启用' : '禁用'}</Tag> },
        { title: '默认', dataIndex: 'is_default', key: 'is_default', render: isDefault => isDefault ? <Tag color="blue">是</Tag> : '否' },
        { title: '操作', key: 'action', render: (_, record) => (
            <Space>
                <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
                <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record.id)}>
                    <Button type="link" danger>删除</Button>
                </Popconfirm>
            </Space>
        )}
    ];

    return (
        <>
            <Button icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>添加支付方式</Button>
            <Table rowKey="id" dataSource={data} columns={columns} loading={loading} />
            <Modal title={editingItem ? '编辑支付方式' : '添加支付方式'} open={modalVisible} onOk={handleOk} onCancel={() => setModalVisible(false)}>
                <Form form={form} layout="vertical" initialValues={{ is_active: true, is_default: false }}>
                    <Form.Item name="name" label="支付方式名称" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="type" label="支付类型">
                        <Input placeholder="例如: online, cash" />
                    </Form.Item>
                    <Form.Item name="is_active" label="状态" valuePropName="checked">
                        <Switch />
                    </Form.Item>
                    <Form.Item name="is_default" label="默认支付方式" valuePropName="checked">
                        <Switch />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

const SalesManagement = () => {
  return (
    <Card>
      <Title level={4}>销售基础数据管理</Title>
      <Tabs defaultActiveKey="platforms">
        <TabPane tab="渠道平台" key="platforms">
          <PlatformManager />
        </TabPane>
        <TabPane tab="平台服务" key="services">
          <ServiceManager />
        </TabPane>
        <TabPane tab="业务模式" key="modes">
          <BusinessModeManager />
        </TabPane>
        <TabPane tab="支付方式" key="payments">
          <PaymentMethodManager />
        </TabPane>
        <TabPane tab="渠道模板" key="templates">
          <ChannelTemplateManagement />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default SalesManagement; 